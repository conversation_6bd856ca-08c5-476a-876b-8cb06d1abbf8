import hashlib
import time
import requests
from typing import List, Dict,  Optional, Union
from datetime import datetime, timedelta
from common.error.detailed_value_error import raise_value_error, ErrorCode, DetailedValueError
from common.constant.beacon_sql_constants import BeaconSQLConstants
from common.constant.download_install_sql_constants import DownloadInstallSQLConstants



# 参考文档：https://doc.beacon.woa.com/page/openapi?app=%E6%95%B0%E6%8D%AE%E6%9F%A5%E8%AF%A2&interface=getCardAnalysisResultUsingGET
# API申请指南：https://iwiki.woa.com/p/4007758100
# Datatalk如何通过Openapi 传自定义SQL查询数据(不依赖图卡)：https://doc.weixin.qq.com/doc/w3_ABsATwaJACkCNIhF0r9d8TBS724Wr?scode=AJEAIQdfAAo7Yqud2EAUkA7gZiAPw


class BeaconClient:
    def __init__(self, application_id: int = 2235, secret_key: str = "B40FF43E210D1B6363D3CA37DA2770B4"):
        """
        初始化客户端

        Args:
            application_id: 应用ID，默认为2235
            secret_key: 密钥，默认为"B40FF43E210D1B6363D3CA37DA2770B4"
        """
        self.application_id = application_id
        self.secret_key = secret_key
        self.session = requests.Session()
        self.sign_version = 'v1'
        self.base_url = "https://api.beacon.woa.com/openapi"

    def _get_sign(self) -> tuple[str, str]:
        """
        生成签名和时间戳
        签名算法: sha256(sha256(secretKey + '-' + timestamp) + '-' + appId)
        timestamp为13位毫秒时间戳

        Returns:
            tuple[str, str]: (timestamp, sign) 元组，包含时间戳和签名
        """
        timestamp = str(int(time.time() * 1000))
        key_hl_1 = hashlib.sha256()
        key_hl_1.update(f"{self.secret_key}-{timestamp}".encode('utf-8'))
        secret_key_sha256 = key_hl_1.hexdigest()

        key_hl_2 = hashlib.sha256()
        key_hl_2.update(f"{secret_key_sha256}-{self.application_id}".encode('utf-8'))
        sign = key_hl_2.hexdigest()

        return timestamp, sign

    def _get_headers(self, content_type="application/x-www-form-urlencoded") -> dict:
        """
        获取请求头信息

        Args:
            content_type: 内容类型，默认为"application/x-www-form-urlencoded"

        Returns:
            dict: 包含认证信息的请求头字典
        """
        timestamp, sign = self._get_sign()
        headers = {
            "bc-api-app-id": str(self.application_id),
            "bc-api-sign-version": self.sign_version,
            "bc-api-timestamp": timestamp,
            "bc-api-sign": sign,
            "Content-Type": content_type
        }
        return headers

    def post_model_query(self, sql: str, data_source_id: int) -> requests.Response:
        """
        自定义sql查询数据接口

        Args:
            sql: SQL查询语句
            data_source_id: 数据源ID

        Returns:
            requests.Response: API响应对象

        Raises:
            DetailedValueError: 当API调用失败时
        """
        body = {
            "bizId": "yyb_bi",
            "plugin_model_conf": {
                "special_model_info": {
                    "sql": sql,
                    "sqlTpl": sql,
                    "sqlSyntaxCheck": True,
                },
                "common_model_info": {
                    "data_source_id": data_source_id, 
                }
            },
            "cacheable": False,
            "version": 1,
            "model_source": 1,
            "canBeaconTurbo": True,
            "model_type": "sql",
            "is_edit": True
        }
        url = f"{self.base_url}/datatalk/analysis/model?bizId=yyb_bi"
        headers = self._get_headers(content_type="application/json")

        response = self.session.post(url, headers=headers, json=body)
        return response

    def query_online_user_count(self, start_date: str = None, end_date: str = None, qua_list: List[str] = None) -> Dict[str, Dict[str, int]]:
        """
        查询联网用户数，表：dws_ydc_networking_guid_di，数据源ID：1235751

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param qua_list: QUA列表，默认为空列表
        :return: 返回嵌套字典，格式为 {date: {qua: user_count}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认QUA列表
        if qua_list is None:
            qua_list = []

        # 如果QUA列表为空，直接返回空结果
        if not qua_list:
            return {}
        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 构建SQL查询
        sql = BeaconSQLConstants.get_online_user_count_sql(
            start_date_formatted, end_date_formatted, qua_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            ds = obj.get('ds')
            qua = obj.get('qua')
            user_count = obj.get('count(DISTINCT guid)', 0)

            # 将日期格式从YYYYMMDD转换为YYYY/M/D
            date_str = str(ds)
            year = date_str[:4]
            month = str(int(date_str[4:6]))  # 去掉前导0
            day = str(int(date_str[6:8]))    # 去掉前导0
            formatted_date = f"{year}/{month}/{day}"

            # 构建嵌套字典
            if formatted_date not in result:
                result[formatted_date] = {}

            result[formatted_date][qua] = user_count

        return result

    def get_user_count_by_date_qua(self, data: Dict[str, Dict[str, int]], date: str, qua: str) -> int:
        """
        获取指定日期和QUA的联网用户数

        :param data: 通过query_online_user_count_optimized获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称
        :return: 联网用户数，如果不存在则返回0
        """
        return data.get(date, {}).get(qua, 0)

    def get_user_count_by_date(self, data: Dict[str, Dict[str, int]], date: str) -> Dict[str, int]:
        """
        获取指定日期的所有QUA联网用户数

        :param data: 通过query_online_user_count_optimized获取的数据
        :param date: 日期，格式：YYYY/M/D
        :return: 该日期下所有QUA的用户数字典，格式为 {qua: user_count}
        """
        return data.get(date, {})

    def get_user_count_by_qua(self, data: Dict[str, Dict[str, int]], qua: str) -> Dict[str, int]:
        """
        获取指定QUA在所有日期的联网用户数

        :param data: 通过query_online_user_count_optimized获取的数据
        :param qua: QUA名称
        :return: 该QUA在所有日期的用户数字典，格式为 {date: user_count}
        """
        result = {}
        for date, qua_data in data.items():
            if qua in qua_data:
                result[date] = qua_data[qua]
        return result

    def query_launch_speed_analytics(self,
                                   qua_list: List[str],
                                   start_date: str = "2024-11-24",
                                   end_date: str = "2025-12-29") -> Dict[str, Dict[str, Dict[str, Dict[str, float]]]]:
        """
        查询启动速度分析数据，支持外部自定义QUA、时间参数，返回启动次数和平均值

        :param qua_list: QUA列表
        :param start_date: 开始日期，格式：YYYY-MM-DD，默认：2024-11-24
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认：2025-12-29
        :return: 返回字典，格式为 {qua: {user_type: {launch_type: {"次数": count, "平均值": average}}}}
                user_type包括: 新用户, 老用户
                launch_type包括: 常规热启动, 常规冷启动, 常规外call热启动, 常规外call冷启动
        """
        # 将日期格式从YYYY-MM-DD转换为YYYYMMDDHH
        start_date_formatted = start_date.replace('-', '') + '00'
        end_date_formatted = end_date.replace('-', '') + '23'

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 构建SQL查询 - 基于用户提供的参考SQL，但不按日期分组以获取整体平均值
        sql = BeaconSQLConstants.get_launch_speed_analytics_sql(
            start_date_formatted, end_date_formatted, qua_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        # 定义目标启动类型
        # 新用户（不带套壳）- 只关注常规启动类型
        new_user_target_types = {'常规热启动', '常规冷启动', '常规外call热启动', '常规外call冷启动'}
        # 老用户（带套壳）- 只关注套壳常规启动类型
        old_user_target_types = {'套壳常规热启动', '套壳常规冷启动', '套壳常规外call热启动', '套壳常规外call冷启动'}

        for obj in objects:
            qua = obj.get('qua')
            startup_type = obj.get('desc_str')
            avg_value = obj.get('平均值', 0)
            count_value = obj.get('次数', 0)

            # 判断是否为目标启动类型
            if startup_type in new_user_target_types:
                user_type = '新用户'
                launch_type = startup_type
            elif startup_type in old_user_target_types:
                user_type = '老用户'
                # 去掉"套壳"前缀
                launch_type = startup_type[2:]  # 去掉前两个字符"套壳"
            else:
                continue  # 跳过非目标启动类型

            # 构建嵌套字典
            if qua not in result:
                result[qua] = {}
            if user_type not in result[qua]:
                result[qua][user_type] = {}

            # 存储次数和平均值，保留2位小数
            result[qua][user_type][launch_type] = {
                "次数": int(count_value) if count_value else 0,
                "平均值": round(avg_value, 2) if avg_value else 0
            }

        return result

    def get_new_user_launch_speed(self, data: Dict[str, Dict[str, Dict[str, Dict[str, float]]]], qua: str = None) -> Dict[str, Dict[str, float]]:
        """
        获取新用户启动速度数据

        :param data: 通过query_launch_speed_analytics获取的数据
        :param qua: 指定QUA，如果为None则返回所有QUA的聚合数据
        :return: 返回字典，格式为 {launch_type: {"次数": count, "平均值": average}}
        """
        if qua:
            return data.get(qua, {}).get('新用户', {})

        # 聚合所有QUA的新用户数据
        aggregated_counts = {}
        aggregated_averages = {}
        for qua_data in data.values():
            new_user_data = qua_data.get('新用户', {})
            for launch_type, metrics in new_user_data.items():
                if launch_type not in aggregated_counts:
                    aggregated_counts[launch_type] = []
                    aggregated_averages[launch_type] = []

                count_value = metrics.get('次数', 0)
                avg_value = metrics.get('平均值', 0)

                if count_value > 0:  # 只考虑有效值
                    aggregated_counts[launch_type].append(count_value)
                if avg_value > 0:  # 只考虑有效值
                    aggregated_averages[launch_type].append(avg_value)

        # 计算聚合结果
        result = {}
        for launch_type in set(list(aggregated_counts.keys()) + list(aggregated_averages.keys())):
            count_values = aggregated_counts.get(launch_type, [])
            avg_values = aggregated_averages.get(launch_type, [])

            result[launch_type] = {
                "次数": sum(count_values) if count_values else 0,
                "平均值": round(sum(avg_values) / len(avg_values), 2) if avg_values else 0
            }

        return result

    def get_old_user_launch_speed(self, data: Dict[str, Dict[str, Dict[str, Dict[str, float]]]], qua: str = None) -> Dict[str, Dict[str, float]]:
        """
        获取老用户启动速度数据

        :param data: 通过query_launch_speed_analytics获取的数据
        :param qua: 指定QUA，如果为None则返回所有QUA的聚合数据
        :return: 返回字典，格式为 {launch_type: {"次数": count, "平均值": average}}
        """
        if qua:
            return data.get(qua, {}).get('老用户', {})

        # 聚合所有QUA的老用户数据
        aggregated_counts = {}
        aggregated_averages = {}
        for qua_data in data.values():
            old_user_data = qua_data.get('老用户', {})
            for launch_type, metrics in old_user_data.items():
                if launch_type not in aggregated_counts:
                    aggregated_counts[launch_type] = []
                    aggregated_averages[launch_type] = []

                count_value = metrics.get('次数', 0)
                avg_value = metrics.get('平均值', 0)

                if count_value > 0:  # 只考虑有效值
                    aggregated_counts[launch_type].append(count_value)
                if avg_value > 0:  # 只考虑有效值
                    aggregated_averages[launch_type].append(avg_value)

        # 计算聚合结果
        result = {}
        for launch_type in set(list(aggregated_counts.keys()) + list(aggregated_averages.keys())):
            count_values = aggregated_counts.get(launch_type, [])
            avg_values = aggregated_averages.get(launch_type, [])

            result[launch_type] = {
                "次数": sum(count_values) if count_values else 0,
                "平均值": round(sum(avg_values) / len(avg_values), 2) if avg_values else 0
            }

        return result

    def query_network_coverage_data(self, date: str = None) -> int:
        """
        查询指定日期的联网用户总数，用于计算联网覆盖率

        :param date: 日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :return: 联网用户总数
        """
        # 设置默认日期为当前时间的前一天
        if date is None:
            date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        date_formatted = date.replace('-', '')

        # 构建SQL查询 - 查询总的联网用户数（不按QUA分组）
        sql = BeaconSQLConstants.get_network_coverage_data_sql(date_formatted)

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据
        objects = response_data.get('data', {}).get('objects', [])

        if not objects:
            return 0

        # 获取总联网用户数
        total_users = objects[0].get('total_networking_users', 0)
        return total_users

    def calculate_network_coverage_rate(self,
                                      online_user_data: Dict[str, Dict[str, int]],
                                      date: str,
                                      total_networking_users: int = None) -> Dict[str, float]:
        """
        计算联网覆盖率：版本联网数/联网用户总数

        :param online_user_data: 通过query_online_user_count获取的数据
        :param date: 日期，格式：YYYY/M/D（与online_user_data中的日期格式一致）
        :param total_networking_users: 联网用户总数，如果为None则自动查询
        :return: 返回字典，格式为 {qua: coverage_rate}
        """
        # 获取指定日期的QUA用户数据
        date_data = self.get_user_count_by_date(online_user_data, date)

        if not date_data:
            return {}

        # 如果没有提供总联网用户数，则自动查询
        if total_networking_users is None:
        # 将日期格式从YYYY/M/D转换为YYYY-MM-DD
            date_parts = date.split('/')
            year = date_parts[0]
            month = date_parts[1].zfill(2)  # 补零
            day = date_parts[2].zfill(2)    # 补零
            formatted_date = f"{year}-{month}-{day}"

            total_networking_users = self.query_network_coverage_data(formatted_date)

        if total_networking_users == 0:
            return {}

        # 计算每个QUA的联网覆盖率
        coverage_rates = {}
        for qua, version_networking_count in date_data.items():
            coverage_rate = round(version_networking_count / total_networking_users, 4)
            coverage_rates[qua] = coverage_rate

        return coverage_rates

    def get_network_coverage_rate_by_qua(self,
                                       coverage_data: Dict[str, float],
                                       qua: str) -> float:
        """
        获取指定QUA的联网覆盖率

        :param coverage_data: 通过calculate_network_coverage_rate获取的数据
        :param qua: QUA名称
        :return: 联网覆盖率，如果不存在则返回0
        """
        return coverage_data.get(qua, 0)

    def query_external_call_download_data(self,
                                        start_date: str = None,
                                        end_date: str = None,
                                        qua_list: List[str] = None) -> Dict[str, Dict[str, any]]:
        """
        查询外call下载数据，包括开始下载率和成功下载率

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param qua_list: QUA列表，默认为空列表
        :return: 返回嵌套字典，格式为 {date: {qua: {metric_name: value}}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认QUA列表
        if qua_list is None:
            qua_list = []

        # 如果QUA列表为空，直接返回空结果
        if not qua_list:
            return {}

        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD和YYYYMMDDHH
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')
        start_date_hour_formatted = start_date.replace('-', '') + '00'
        end_date_hour_formatted = end_date.replace('-', '') + '23'

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 构建SQL查询
        sql = BeaconSQLConstants.get_external_call_download_rate_sql(
            start_date_formatted, end_date_formatted,
            start_date_hour_formatted, end_date_hour_formatted,
            qua_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            ds = obj.get('日期')
            qua = obj.get('版本')

            # 将日期格式从YYYYMMDD转换为YYYY/M/D
            date_str = str(ds)
            year = date_str[:4]
            month = str(int(date_str[4:6]))  # 去掉前导0
            day = str(int(date_str[6:8]))    # 去掉前导0
            formatted_date = f"{year}/{month}/{day}"

            # 构建嵌套字典
            if formatted_date not in result:
                result[formatted_date] = {}

            if qua not in result[formatted_date]:
                result[formatted_date][qua] = {}

            # 存储所有指标数据
            result[formatted_date][qua] = {
                '外call承接户数': obj.get('外call承接户数', 0),
                '外call曝光户数': obj.get('外call曝光户数', 0),
                '外call卡曝光率': obj.get('外call卡曝光率', '0%'),
                '外call承接曝光pv': obj.get('外call承接曝光pv', 0),
                '外call曝光pv': obj.get('外call曝光pv', 0),
                '开始下载户数': obj.get('开始下载户数', 0),
                '外call开始下载率': obj.get('外call开始下载率', '0%'),
                '成功下载户数': obj.get('成功下载户数', 0),
                '外call成功下载率': obj.get('外call成功下载率', '0%'),
                '开始下载量': obj.get('开始下载量', 0),
                '成功下载量': obj.get('成功下载量', 0),
                '失败下载量': obj.get('失败下载量', 0),
                '暂停下载量': obj.get('暂停下载量', 0),
                '失败下载户数': obj.get('失败下载户数', 0),
                '暂停下载户数': obj.get('暂停下载户数', 0)
            }

        return result

    def get_external_call_start_download_rate(self,
                                             data: Dict[str, Dict[str, Dict[str, any]]],
                                             date: str,
                                             qua: str = None) -> Dict[str, str]:
        """
        获取外call开始下载率

        :param data: 通过query_external_call_download_data获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的开始下载率
        :return: 如果指定QUA，返回该QUA的开始下载率字符串；否则返回字典 {qua: rate}
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('外call开始下载率', '0%')

        # 返回该日期所有QUA的开始下载率
        result = {}
        for qua_name, qua_data in date_data.items():
            result[qua_name] = qua_data.get('外call开始下载率', '0%')

        return result

    def get_external_call_success_download_rate(self,
                                               data: Dict[str, Dict[str, Dict[str, any]]],
                                               date: str,
                                               qua: str = None) -> Dict[str, str]:
        """
        获取外call成功下载率

        :param data: 通过query_external_call_download_data获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的成功下载率
        :return: 如果指定QUA，返回该QUA的成功下载率字符串；否则返回字典 {qua: rate}
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('外call成功下载率', '0%')

        # 返回该日期所有QUA的成功下载率
        result = {}
        for qua_name, qua_data in date_data.items():
            result[qua_name] = qua_data.get('外call成功下载率', '0%')

        return result

    def get_external_call_download_metrics_by_qua(self,
                                                 data: Dict[str, Dict[str, Dict[str, any]]],
                                                 qua: str) -> Dict[str, Dict[str, any]]:
        """
        获取指定QUA在所有日期的外call下载指标

        :param data: 通过query_external_call_download_data获取的数据
        :param qua: QUA名称
        :return: 该QUA在所有日期的下载指标字典，格式为 {date: {metric_name: value}}
        """
        result = {}
        for date, date_data in data.items():
            if qua in date_data:
                result[date] = date_data[qua]
        return result

    def query_cloud_gaming_plugin_launch_success_rate(self,
                                                     start_date: str = None,
                                                     end_date: str = None,
                                                     app_version_list: List[str] = None) -> Dict[str, Dict[str, Dict[str, float]]]:
        """
        查询云游插件拉起成功率数据

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param app_version_list: app_version列表，默认为空列表
        :return: 返回嵌套字典，格式为 {date: {app_version: {metric_name: value}}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认app_version列表
        if app_version_list is None:
            app_version_list = []

        # 如果app_version列表为空，直接返回空结果
        if not app_version_list:
            return {}

        # 将日期格式转换为所需格式
        # start_date_hour_formatted: YYYYMMDDHH
        start_date_hour_formatted = start_date.replace('-', '') + '00'

        # end_date_hour_formatted_plus_one: 结束日期+1天的00时
        end_date_dt = datetime.strptime(end_date, '%Y-%m-%d')
        end_date_plus_one = (end_date_dt + timedelta(days=1)).strftime('%Y%m%d') + '01'

        # start_date_time: YYYY-MM-DD HH:MM:SS
        start_date_time = start_date + ' 00:00:00'

        # end_date_time_plus_one: 结束日期+1天的00:00:00
        end_date_time_plus_one = (end_date_dt + timedelta(days=1)).strftime('%Y-%m-%d') + ' 00:00:00'

        # 构建app_version条件
        app_version_conditions = "', '".join(app_version_list)
        app_version_conditions = f"'{app_version_conditions}'"

        # 构建SQL查询
        sql = BeaconSQLConstants.get_cloud_gaming_plugin_launch_success_rate_sql(
            start_date_hour_formatted, end_date_plus_one,
            start_date_time, end_date_time_plus_one,
            app_version_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            date = obj.get('dim_0')  # 日期格式：YYYY-MM-DD
            app_version = obj.get('dim_1')

            # 获取各项指标
            tamst_users = obj.get('tamst跳转用户数', 0)  # tamst跳转用户数
            open_activity_users = obj.get('openActivity用户数', 0)  # openActivity用户数
            open_activity_start_users = obj.get('openActivity-实际启动用户数', 0)  # openActivity-实际启动用户数
            click_to_launch_rate = obj.get('端内插件点击到拉起率', 0)  # 端内插件点击到拉起率
            click_to_execution_rate = obj.get('端内插件点击到实际执行率', 0)  # 端内插件点击到实际执行率（云游插件拉起成功率）

            # 构建嵌套字典
            if date not in result:
                result[date] = {}

            if app_version not in result[date]:
                result[date][app_version] = {}

            # 存储所有指标数据
            result[date][app_version] = {
                'tamst跳转用户数': tamst_users,
                'openActivity用户数': open_activity_users,
                'openActivity实际启动用户数': open_activity_start_users,
                '端内插件点击到拉起率': round(click_to_launch_rate, 4) if click_to_launch_rate else 0,
                '端内插件点击到实际执行率': round(click_to_execution_rate, 4) if click_to_execution_rate else 0,
                '云游插件拉起成功率': round(click_to_execution_rate, 4) if click_to_execution_rate else 0  # 别名
            }

        return result

    def get_cloud_gaming_plugin_launch_success_rate(self,
                                                   data: Dict[str, Dict[str, Dict[str, float]]],
                                                   date: str,
                                                   app_version: str = None) -> float:
        """
        获取云游插件拉起成功率

        :param data: 通过query_cloud_gaming_plugin_launch_success_rate获取的数据
        :param date: 日期，格式：YYYY-MM-DD
        :param app_version: app_version名称，如果为None则返回该日期所有版本的平均成功率
        :return: 云游插件拉起成功率（0-1之间的小数）
        """
        date_data = data.get(date, {})

        if app_version:
            app_data = date_data.get(app_version, {})
            return app_data.get('云游插件拉起成功率', 0)

        # 返回该日期所有版本的平均成功率
        if not date_data:
            return 0

        rates = [app_data.get('云游插件拉起成功率', 0) for app_data in date_data.values()]
        valid_rates = [rate for rate in rates if rate > 0]

        if not valid_rates:
            return 0

        return round(sum(valid_rates) / len(valid_rates), 4)

    def get_cloud_gaming_plugin_click_to_launch_rate(self,
                                                   data: Dict[str, Dict[str, Dict[str, float]]],
                                                   date: str,
                                                   app_version: str = None) -> float:
        """
        获取端内插件点击到拉起率

        :param data: 通过query_cloud_gaming_plugin_launch_success_rate获取的数据
        :param date: 日期，格式：YYYY-MM-DD
        :param app_version: app_version名称，如果为None则返回该日期所有版本的平均拉起率
        :return: 端内插件点击到拉起率（0-1之间的小数）
        """
        date_data = data.get(date, {})

        if app_version:
            app_data = date_data.get(app_version, {})
            return app_data.get('端内插件点击到拉起率', 0)

        # 返回该日期所有版本的平均拉起率
        if not date_data:
            return 0

        rates = [app_data.get('端内插件点击到拉起率', 0) for app_data in date_data.values()]
        valid_rates = [rate for rate in rates if rate > 0]

        if not valid_rates:
            return 0

        return round(sum(valid_rates) / len(valid_rates), 4)

    def get_cloud_gaming_plugin_metrics_by_app_version(self,
                                                     data: Dict[str, Dict[str, Dict[str, float]]],
                                                     app_version: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定app_version在所有日期的云游插件指标

        :param data: 通过query_cloud_gaming_plugin_launch_success_rate获取的数据
        :param app_version: app_version名称
        :return: 该app_version在所有日期的指标字典，格式为 {date: {metric_name: value}}
        """
        result = {}
        for date, date_data in data.items():
            if app_version in date_data:
                result[date] = date_data[app_version]
        return result

    def get_cloud_gaming_plugin_metrics_by_date(self,
                                              data: Dict[str, Dict[str, Dict[str, float]]],
                                              date: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定日期的所有app_version云游插件指标

        :param data: 通过query_cloud_gaming_plugin_launch_success_rate获取的数据
        :param date: 日期，格式：YYYY-MM-DD
        :return: 该日期下所有app_version的指标字典，格式为 {app_version: {metric_name: value}}
        """
        return data.get(date, {})

    def query_popup_success_rate(self,
                                start_date: str = None,
                                end_date: str = None,
                                qua_list: List[str] = None,
                                user_type_list: List[str] = None) -> Dict[str, Dict[str, Dict[str, float]]]:
        """
        查询弹窗成功率数据

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param qua_list: QUA列表，默认为空列表
        :param user_type_list: 用户类型列表，默认为['1', '2']（新用户和老用户）
        :return: 返回嵌套字典，格式为 {date: {qua: {metric_name: value}}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认QUA列表
        if qua_list is None:
            qua_list = []

        # 设置默认用户类型列表
        if user_type_list is None:
            user_type_list = ['remain', 'silent_back']  

        # 如果QUA列表为空，直接返回空结果
        if not qua_list:
            return {}

        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 构建用户类型条件
        user_type_conditions = "', '".join(user_type_list)
        user_type_conditions = f"'{user_type_conditions}'"

        # 构建SQL查询
        sql = BeaconSQLConstants.get_popup_success_rate_sql(
            start_date_formatted, end_date_formatted,
            qua_conditions, user_type_conditions
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            ds = obj.get('时间')
            qua = obj.get('版本号')
            pop_times = obj.get('弹窗人数', 0)
            expose_times = obj.get('曝光人数', 0)
            success_rate = obj.get('弹窗成功率(%)', 0)

            # 将日期格式从YYYYMMDD转换为YYYY/M/D
            date_str = str(ds)
            year = date_str[:4]
            month = str(int(date_str[4:6]))  # 去掉前导0
            day = str(int(date_str[6:8]))    # 去掉前导0
            formatted_date = f"{year}/{month}/{day}"

            # 构建嵌套字典
            if formatted_date not in result:
                result[formatted_date] = {}

            if qua not in result[formatted_date]:
                result[formatted_date][qua] = {}

            # 存储所有指标数据
            result[formatted_date][qua] = {
                '弹窗人数': pop_times,
                '曝光人数': expose_times,
                '弹窗成功率(%)': success_rate
            }

        return result

    def get_popup_success_rate(self,
                             data: Dict[str, Dict[str, Dict[str, float]]],
                             date: str,
                             qua: str = None) -> float:
        """
        获取弹窗成功率

        :param data: 通过query_popup_success_rate获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的平均成功率
        :return: 弹窗成功率（百分比数值）
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('弹窗成功率(%)', 0)

        # 返回该日期所有QUA的平均成功率
        if not date_data:
            return 0

        rates = [qua_data.get('弹窗成功率(%)', 0) for qua_data in date_data.values()]
        valid_rates = [rate for rate in rates if rate > 0]

        if not valid_rates:
            return 0

        return round(sum(valid_rates) / len(valid_rates), 2)

    def get_popup_metrics_by_qua(self,
                               data: Dict[str, Dict[str, Dict[str, float]]],
                               qua: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定QUA在所有日期的弹窗指标

        :param data: 通过query_popup_success_rate获取的数据
        :param qua: QUA名称
        :return: 该QUA在所有日期的指标字典，格式为 {date: {metric_name: value}}
        """
        result = {}
        for date, date_data in data.items():
            if qua in date_data:
                result[date] = date_data[qua]
        return result

    def get_popup_metrics_by_date(self,
                                data: Dict[str, Dict[str, Dict[str, float]]],
                                date: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定日期的所有QUA弹窗指标

        :param data: 通过query_popup_success_rate获取的数据
        :param date: 日期，格式：YYYY/M/D
        :return: 该日期下所有QUA的指标字典，格式为 {qua: {metric_name: value}}
        """
        return data.get(date, {})

    def get_popup_exposure_rate(self,
                              data: Dict[str, Dict[str, Dict[str, float]]],
                              date: str,
                              qua: str = None) -> Dict[str, float]:
        """
        获取弹窗曝光率- 曝光人数/弹窗人数

        :param data: 通过query_popup_success_rate获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的曝光率
        :return: 如果指定QUA，返回该QUA的曝光率；否则返回字典 {qua: rate}
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            pop_times = qua_data.get('弹窗人数', 0)
            expose_times = qua_data.get('曝光人数', 0)
            if pop_times > 0:
                return round((expose_times / pop_times) * 100, 2)
            return 0

        # 返回该日期所有QUA的曝光率
        result = {}
        for qua_name, qua_data in date_data.items():
            pop_times = qua_data.get('弹窗人数', 0)
            expose_times = qua_data.get('曝光人数', 0)
            if pop_times > 0:
                result[qua_name] = round((expose_times / pop_times) * 100, 2)
            else:
                result[qua_name] = 0

        return result

    def query_download_install_cvr(self,
                                 start_date: str = None,
                                 end_date: str = None,
                                 qua_list: List[str] = None,
                                 **kwargs) -> Dict[str, Dict[str, Dict[str, any]]]:
        """
        查询下载安装CVR数据

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param qua_list: QUA列表，默认为['TMAF_888_F_5472','TMAF_888_F_5739']
        :param kwargs: 其他SQL参数
        :return: 返回嵌套字典，格式为 {date: {qua: {metric_name: value}}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认QUA列表
        if qua_list is None:
            qua_list = ['TMAF_888_F_5472', 'TMAF_888_F_5739']

        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')

        # 构建QUA条件
        qua_conditions = "', '".join(qua_list)
        qua_conditions = f"'{qua_conditions}'"

        # 准备SQL参数
        sql_params = {
            'tr1_start': start_date_formatted,
            'tr1_end': end_date_formatted,
            'select_qua': qua_conditions,
            'select_expand_ds': "'expand'",  # 确保显示具体日期
            **kwargs
        }

        # 构建SQL查询
        sql = DownloadInstallSQLConstants.get_download_install_cvr_sql(**sql_params)

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            date = obj.get('日期')
            qua = obj.get('qua')

            # 将日期格式从YYYYMMDD转换为YYYY/M/D
            if date and date != '合计':
                date_str = str(date)
                year = date_str[:4]
                month = str(int(date_str[4:6]))  # 去掉前导0
                day = str(int(date_str[6:8]))    # 去掉前导0
                formatted_date = f"{year}/{month}/{day}"
            else:
                formatted_date = str(date) if date else '未知'

            # 构建嵌套字典
            if formatted_date not in result:
                result[formatted_date] = {}

            if qua not in result[formatted_date]:
                result[formatted_date][qua] = {}

            # 存储所有指标数据 - 使用API返回的实际字段名
            result[formatted_date][qua] = {
                '开始下载数': obj.get('开始下载数', 0),
                '下载成功数': obj.get('下载成功数', 0),
                '安装成功数': obj.get('安装成功数', 0),
                '下载CVR': obj.get('下载cvr', 0),  # 注意：API返回的是小写
                '安装CVR': obj.get('安装cvr', 0),  # 注意：API返回的是小写
                '下载安装CVR': obj.get('下载安装cvr', 0),  # 注意：API返回的是小写
                '快速安装数': obj.get('快速安装数', 0),
                '快速安装率': obj.get('快速安装率', 0),
                '洗包率': obj.get('洗包率', 0),
                '拉起系统安装率': obj.get('拉起系统安装率', 0),
                '系统安装完成率': obj.get('系统安装完成率', 0),
                '下载删除率': obj.get('下载删除率', 0),
                '下载暂停率': obj.get('下载暂停率', 0),
                '下载失败率': obj.get('下载失败率', 0),
                '下载未完率': obj.get('下载未完率', 0),
                '恶意占比': obj.get('恶意占比', 0),
                '预约静默下载占比': obj.get('预约静默下载占比', 0),
                '下载成功前台率': obj.get('下载成功前台率', 0),
                '安装入队前台率': obj.get('安装入队前台率', 0),
                '安装入队关屏率': obj.get('安装入队关屏率', 0),
                '微端比例': obj.get('微端比例', 0),
                '安装重试率': obj.get('安装重试率', 0),
                '前台安装重试率': obj.get('前台安装重试率', 0),
                '安装成功重试率': obj.get('安装成功重试率', 0),
                '平均速度MB': obj.get('平均速度mb', 0),  # 注意：API返回的是小写
                '速度P50': obj.get('速度p50', 0),  # 注意：API返回的是小写
                '速度P75': obj.get('速度p75', 0),  # 注意：API返回的是小写
                '速度P90': obj.get('速度p90', 0),  # 注意：API返回的是小写
                '速度P95': obj.get('速度p95', 0),  # 注意：API返回的是小写
                '速度P99': obj.get('速度p99', 0),  # 注意：API返回的是小写
                'buffer溢出率': obj.get('buffer溢出率', 0)
            }

        return result

    def get_download_cvr(self,
                        data: Dict[str, Dict[str, Dict[str, any]]],
                        date: str,
                        qua: str = None) -> float:
        """
        获取下载CVR

        :param data: 通过query_download_install_cvr获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的平均下载CVR
        :return: 下载CVR
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('下载CVR', 0)

        # 返回该日期所有QUA的平均下载CVR
        if not date_data:
            return 0

        cvr_values = [qua_data.get('下载CVR', 0) for qua_data in date_data.values()]
        valid_values = [cvr for cvr in cvr_values if cvr > 0]

        if not valid_values:
            return 0

        return round(sum(valid_values) / len(valid_values), 2)

    def get_install_cvr(self,
                       data: Dict[str, Dict[str, Dict[str, any]]],
                       date: str,
                       qua: str = None) -> float:
        """
        获取安装CVR

        :param data: 通过query_download_install_cvr获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的平均安装CVR
        :return: 安装CVR
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('安装CVR', 0)

        # 返回该日期所有QUA的平均安装CVR
        if not date_data:
            return 0

        cvr_values = [qua_data.get('安装CVR', 0) for qua_data in date_data.values()]
        valid_values = [cvr for cvr in cvr_values if cvr > 0]

        if not valid_values:
            return 0

        return round(sum(valid_values) / len(valid_values), 2)

    def get_download_install_cvr(self,
                                data: Dict[str, Dict[str, Dict[str, any]]],
                                date: str,
                                qua: str = None) -> float:
        """
        获取下载安装CVR

        :param data: 通过query_download_install_cvr获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param qua: QUA名称，如果为None则返回该日期所有QUA的平均下载安装CVR
        :return: 下载安装CVR
        """
        date_data = data.get(date, {})

        if qua:
            qua_data = date_data.get(qua, {})
            return qua_data.get('下载安装CVR', 0)

        # 返回该日期所有QUA的平均下载安装CVR
        if not date_data:
            return 0

        cvr_values = [qua_data.get('下载安装CVR', 0) for qua_data in date_data.values()]
        valid_values = [cvr for cvr in cvr_values if cvr > 0]

        if not valid_values:
            return 0

        return round(sum(valid_values) / len(valid_values), 2)

    def get_download_install_metrics_by_qua(self,
                                          data: Dict[str, Dict[str, Dict[str, any]]],
                                          qua: str) -> Dict[str, Dict[str, any]]:
        """
        获取指定QUA在所有日期的下载安装指标

        :param data: 通过query_download_install_cvr获取的数据
        :param qua: QUA名称
        :return: 该QUA在所有日期的指标字典，格式为 {date: {metric_name: value}}
        """
        result = {}
        for date, date_data in data.items():
            if qua in date_data:
                result[date] = date_data[qua]
        return result

    def get_download_install_metrics_by_date(self,
                                           data: Dict[str, Dict[str, Dict[str, any]]],
                                           date: str) -> Dict[str, Dict[str, any]]:
        """
        获取指定日期的所有QUA下载安装指标

        :param data: 通过query_download_install_cvr获取的数据
        :param date: 日期，格式：YYYY/M/D
        :return: 该日期下所有QUA的指标字典，格式为 {qua: {metric_name: value}}
        """
        return data.get(date, {})

    def query_advertisement_data(self,
                               start_date: str = None,
                               end_date: str = None,
                               version_list: List[str] = None,
                               scene_split: bool = False) -> Dict[str, Dict[str, Dict[str, any]]]:
        """
        查询广告数据（曝光、点击、下载、安装）及转化率

        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当前时间的前一天
        :param version_list: 版本列表，默认为['all']
        :param scene_split: 是否按场景分组，默认False
        :return: 返回嵌套字典，格式为 {date: {version+scene: {metric_name: value}}}
        """
        # 设置默认日期为当前时间的前一天
        if start_date is None or end_date is None:
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            if start_date is None:
                start_date = yesterday
            if end_date is None:
                end_date = yesterday

        # 设置默认版本列表
        if version_list is None:
            version_list = []

        # 将日期格式从YYYY-MM-DD转换为YYYYMMDD
        start_date_formatted = start_date.replace('-', '')
        end_date_formatted = end_date.replace('-', '')

        # 构建版本条件
        version_conditions = "', '".join(version_list)
        version_conditions = f"'{version_conditions}'"

        # 设置是否按场景分组
        is_scene = 1 if scene_split else 0

        # 构建SQL查询
        sql = BeaconSQLConstants.get_advertisement_data_sql(
            start_date_formatted, end_date_formatted,
            version_conditions, is_scene
        )

        # 执行查询
        response = self.post_model_query(sql=sql, data_source_id=BeaconSQLConstants.AD_DATA_SOURCE_ID)

        if response.status_code != 200:
            raise Exception(f"API请求失败，状态码: {response.status_code}, 响应: {response.text}")

        response_data = response.json()

        # 检查响应状态
        if response_data.get('code') != 0:
            raise Exception(f"查询失败: {response_data.get('message', '未知错误')}")

        # 解析数据并转换为嵌套字典格式
        objects = response_data.get('data', {}).get('objects', [])
        result = {}

        for obj in objects:
            imp_date = obj.get('imp_date')
            yyb_version = obj.get('yyb_version', 'unknown')
            scene = obj.get('scene', 'all')

            # 将日期格式从YYYYMMDD转换为YYYY/M/D
            if imp_date:
                date_str = str(imp_date)
                year = date_str[:4]
                month = str(int(date_str[4:6]))  # 去掉前导0
                day = str(int(date_str[6:8]))    # 去掉前导0
                formatted_date = f"{year}/{month}/{day}"
            else:
                formatted_date = '未知'

            # 构建组合键：版本+场景
            if scene_split:
                version_scene_key = f"{yyb_version}_{scene}"
            else:
                version_scene_key = yyb_version

            # 构建嵌套字典
            if formatted_date not in result:
                result[formatted_date] = {}

            if version_scene_key not in result[formatted_date]:
                result[formatted_date][version_scene_key] = {}

            # 存储所有指标数据
            result[formatted_date][version_scene_key] = {
                '广告曝光': obj.get('total_exp_cnt', 0),
                '广告点击': obj.get('total_click_cnt', 0),
                '广告下载': obj.get('total_dload_cnt', 0),
                '广告安装': obj.get('total_install_cnt', 0),
                '点击曝光率': obj.get('click_exposure_rate', 0),
                '下载点击率': obj.get('download_click_rate', 0),
                '安装下载率': obj.get('install_download_rate', 0),
                '版本': yyb_version,
                '场景': scene
            }

        return result

    def get_advertisement_exposure_count(self,
                                       data: Dict[str, Dict[str, Dict[str, any]]],
                                       date: str,
                                       version: str = None) -> int:
        """
        获取广告曝光数

        :param data: 通过query_advertisement_data获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param version: 版本名称，如果为None则返回该日期所有版本的总曝光数
        :return: 广告曝光数
        """
        date_data = data.get(date, {})

        if version:
            version_data = date_data.get(version, {})
            return version_data.get('广告曝光', 0)

        # 返回该日期所有版本的总曝光数
        total_exposure = 0
        for version_data in date_data.values():
            total_exposure += version_data.get('广告曝光', 0)

        return total_exposure

    def get_advertisement_conversion_rates(self,
                                         data: Dict[str, Dict[str, Dict[str, any]]],
                                         date: str,
                                         version: str = None) -> Dict[str, float]:
        """
        获取广告转化率

        :param data: 通过query_advertisement_data获取的数据
        :param date: 日期，格式：YYYY/M/D
        :param version: 版本名称，如果为None则返回该日期所有版本的平均转化率
        :return: 转化率字典 {rate_name: rate_value}
        """
        date_data = data.get(date, {})

        if version:
            version_data = date_data.get(version, {})
            return {
                '点击曝光率': version_data.get('点击曝光率', 0),
                '下载点击率': version_data.get('下载点击率', 0),
                '安装下载率': version_data.get('安装下载率', 0)
            }

        # 返回该日期所有版本的平均转化率
        if not date_data:
            return {'点击曝光率': 0, '下载点击率': 0, '安装下载率': 0}

        click_rates = [v.get('点击曝光率', 0) for v in date_data.values()]
        download_rates = [v.get('下载点击率', 0) for v in date_data.values()]
        install_rates = [v.get('安装下载率', 0) for v in date_data.values()]

        valid_click_rates = [r for r in click_rates if r > 0]
        valid_download_rates = [r for r in download_rates if r > 0]
        valid_install_rates = [r for r in install_rates if r > 0]

        return {
            '点击曝光率': round(sum(valid_click_rates) / len(valid_click_rates), 2) if valid_click_rates else 0,
            '下载点击率': round(sum(valid_download_rates) / len(valid_download_rates), 2) if valid_download_rates else 0,
            '安装下载率': round(sum(valid_install_rates) / len(valid_install_rates), 2) if valid_install_rates else 0
        }

    def get_advertisement_metrics_by_version(self,
                                           data: Dict[str, Dict[str, Dict[str, any]]],
                                           version: str) -> Dict[str, Dict[str, any]]:
        """
        获取指定版本在所有日期的广告指标

        :param data: 通过query_advertisement_data获取的数据
        :param version: 版本名称
        :return: 该版本在所有日期的指标字典，格式为 {date: {metric_name: value}}
        """
        result = {}
        for date, date_data in data.items():
            if version in date_data:
                result[date] = date_data[version]
        return result

    def get_advertisement_metrics_by_date(self,
                                        data: Dict[str, Dict[str, Dict[str, any]]],
                                        date: str) -> Dict[str, Dict[str, any]]:
        """
        获取指定日期的所有版本广告指标

        :param data: 通过query_advertisement_data获取的数据
        :param date: 日期，格式：YYYY/M/D
        :return: 该日期下所有版本的指标字典，格式为 {version: {metric_name: value}}
        """
        return data.get(date, {})
    def query_active_coverage(
        self,
        qua_list: Optional[List[str]] = None,
        app_version: Optional[Union[str, int, List[Union[str, int]]]] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
    ) -> List[dict]:
        """
        查询活跃覆盖率数据

        Args:
            qua_list: 包名列表，可选
            app_version: 版本号，可选。可以是字符串、整数或它们的列表，如 "899"、899、["899", 900]
            start_time: 开始时间，格式为 "YYYY-MM-DD HH:mm:ss"，默认为昨天
            end_time: 结束时间，格式为 "YYYY-MM-DD HH:mm:ss"，默认为今天

        Returns:
            List[dict]: 包含覆盖率数据的列表，每个元素包含：
                - app_version: 包名或版本号
                - dau: 日活跃用户数
                - coverage_rate: 覆盖率百分比

        Raises:
            DetailedValueError: 
                - 当qua_list和app_version都未提供时
                - 当时间格式无效时
                - 当API请求失败时
                - 当响应数据解析失败时
        """
        if not qua_list and not app_version:
            raise_value_error(
                ErrorCode.MISSING_REQUIRED_FIELD,
                message="必须传入qua_list或app_version中的至少一个"
            )

        def _convert_time_format(time_str: Optional[str], default_offset_days: int = 0) -> int:
            """
            将 YYYY-MM-DD HH:mm:ss 格式转换为 YYYYMMDD 格式的整数

            Args:
                time_str: 时间字符串，格式为 YYYY-MM-DD HH:mm:ss
                default_offset_days: 默认时间偏移天数，0表示今天，-1表示昨天，1表示明天

            Returns:
                int: YYYYMMDD 格式的整数

            Raises:
                DetailedValueError: 当时间格式无效时
            """
            if time_str is None:
                # 使用默认时间（今天或昨天）
                timestamp = time.time() + (default_offset_days * 86400)
                return int(time.strftime("%Y%m%d", time.localtime(timestamp)))
            
            try:
                # 解析输入的时间字符串
                dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                # 转换为 YYYYMMDD 格式的整数
                return int(dt.strftime("%Y%m%d"))
            except ValueError as e:
                raise_value_error(
                    ErrorCode.INVALID_PARAMETER,
                    message="时间格式无效，应为 YYYY-MM-DD HH:mm:ss",
                    context={"time_str": time_str, "error": str(e)}
                )

        # 转换时间格式
        end_date = _convert_time_format(end_time, -1)  # 默认为昨天
        start_date = _convert_time_format(start_time, -1)  # 默认为昨天

        # 验证时间范围
        if start_date > end_date:
            raise_value_error(
                ErrorCode.INVALID_PARAMETER,
                message="开始时间不能晚于结束时间",
                context={"start_date": start_date, "end_date": end_date}
            )

        results = []

        def parse_response(response) -> List[dict]:
            """解析单个响应，提取覆盖率数据"""
            if response.status_code != 200:
                raise_value_error(
                    ErrorCode.BEACON_API_ERROR,
                    message=f"请求失败，状态码: {response.status_code}",
                    context={"response": response.text}
                )

            resp_data = response.json()
            if resp_data.get('code') != 0:
                raise_value_error(
                    ErrorCode.BEACON_API_ERROR,
                    message=f"查询失败: {resp_data.get('message')}",
                    context={"response": resp_data}
                )

            objects = resp_data.get('data', {}).get('objects', [])
            if not objects:
                return []

            try:
                return [{
                    'app_version': obj['app_version'],
                    'dau': int(obj['dau']),
                    'coverage_rate': obj['coverage_rate']
                } for obj in objects]
            except (KeyError, ValueError, TypeError) as e:
                raise_value_error(
                    ErrorCode.BEACON_RESPONSE_ERROR,
                    message="解析响应数据失败",
                    context={"error": str(e), "response": resp_data}
                )

        # 包名查询SQL
        if qua_list:
            qu_str = ",".join(f"'{q}'" for q in qua_list)
            order_case = "CASE ql.app_version\n"
            for idx, qu in enumerate(qua_list, 1):
                order_case += f"    WHEN '{qu}' THEN {idx}\n"
            order_case += "    ELSE 9999\nEND"

            sql_qua = f"""
            WITH base_data AS (
                SELECT
                    ds,
                    last_qua,
                    guid
                FROM beacon_olap.dws_yyb_user_agg_di
                WHERE ds >= {start_date} AND ds <= {end_date}
            ),
            total_guids AS (
                SELECT COUNT(DISTINCT guid) AS total_count
                FROM base_data
            ),
            qu_list_guids AS (
                SELECT last_qua AS app_version, COUNT(DISTINCT guid) AS dau
                FROM base_data
                WHERE last_qua IN ({qu_str})
                GROUP BY last_qua
            )
            SELECT
                ql.app_version,
                ql.dau,
                CONCAT(
                    CAST(ROUND(ql.dau * 100.0 / tg.total_count, 2) AS STRING),
                    '%'
                ) AS coverage_rate
            FROM qu_list_guids ql
            CROSS JOIN total_guids tg
            ORDER BY {order_case}
            LIMIT 5000;
            """
            resp_qua = self.post_model_query(sql=sql_qua, data_source_id=1235751)
            results.extend(parse_response(resp_qua))

        # 版本号查询SQL
        if app_version:
            # 转换为整数版本号列表
            if isinstance(app_version, (str, int)):
                app_versions = [int(app_version)]
            else:
                app_versions = [int(v) for v in app_version]
            
            # 构建版本号IN条件
            version_str = ",".join(str(v) for v in app_versions)
            # 构建版本号排序CASE语句
            order_case = "CASE vg.qua\n"
            for idx, version in enumerate(app_versions, 1):
                order_case += f"    WHEN {version} THEN {idx}\n"
            order_case += "    ELSE 9999\nEND"
            
            sql_version = f"""
            WITH filtered_data AS (
                SELECT
                    ds,
                    guid,
                    CAST(split_part(last_qua, '_', 2) AS int) AS qua
                FROM beacon_olap.dws_yyb_user_agg_di
                WHERE ds >= {start_date} AND ds <= {end_date}
            ),
            version_guids AS (
                SELECT
                    qua,
                    COUNT(DISTINCT guid) AS dau
                FROM filtered_data
                WHERE qua IN ({version_str})
                GROUP BY qua
            ),
            total_guids AS (
                SELECT COUNT(DISTINCT guid) AS total_count
                FROM filtered_data
            )
            SELECT
                CAST(vg.qua AS STRING) AS app_version,
                vg.dau,
                CONCAT(
                    CAST(
                        ROUND(vg.dau * 100.0 / tg.total_count, 2) AS STRING
                    ),
                    '%'
                ) AS coverage_rate
            FROM version_guids vg
            CROSS JOIN total_guids tg
            ORDER BY {order_case}
            LIMIT 5000;
            """
            resp_version = self.post_model_query(sql=sql_version, data_source_id=1235751)
            results.extend(parse_response(resp_version))

        return results


if __name__ == "__main__":
    qu_list = ['TMAF_858_P_9521', 'TMAF_858_P_9522']
    app_versions = [898, 899, 900]  # 测试多个版本号
    
    # 创建 BeaconAPIClient 实例，使用默认的 application_id 和 secret_key
    client = BeaconAPIClient()

    # 只查包名
    results_qua = client.query_active_coverage(qua_list=qu_list)
    print("\n包名查询结果:")
    for result in results_qua:
        print(result)
        print("---")

    # 查询多个版本号
    results_versions = client.query_active_coverage(app_version=app_versions)
    print("\n多版本号查询结果:")
    for result in results_versions:
        print(result)
        print("---")

    # 单个版本号查询（保持向后兼容性）
    results_single_version = client.query_active_coverage(app_version=898)
    print("\n单个版本号查询结果:")
    for result in results_single_version:
        print(result)
        print("---")

    # 包名+多版本号都查
    results_both = client.query_active_coverage(qua_list=qu_list, app_version=app_versions)
    print("\n包名+多版本号查询结果:")
    for result in results_both:
        print(result)
        print("---")

    # 混合字符串和整数版本号查询
    mixed_versions = ["898", 899, "900"]
    results_mixed = client.query_active_coverage(app_version=mixed_versions)
    print("\n混合版本号格式查询结果:")
    for result in results_mixed:
        print(result)
        print("---")

    # 客户端灰度技术指标-联网用户数
    # sql = """
    #     select
    #         ds, qua, count(distinct guid)
    #     from
    #         beacon_olap.dws_ydc_networking_guid_di
    #     where
    #         ds >= 20230801
    #         and ds <= 20251031
    #         and qua in ('TMAF_893_P_2350','TMAF_893_P_2351','TMAF_893_P_2352','TMAF_893_P_2367','TMAF_893_P_2355','TMAF_893_P_2356','TMAF_893_P_2357','TMAF_893_P_2368')
    #     group by ds, qua
    #     order by qua desc
    #     """
    # response = client.post_model_query(sql=sql, data_source_id=1235751)
    # print("post_model_query状态码:", response.status_code)
    # print("post_model_query响应内容:", response.text)
