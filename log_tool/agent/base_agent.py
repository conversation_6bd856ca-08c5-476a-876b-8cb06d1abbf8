import os
import re
from datetime import datetime
from collections import defaultdict

from common.tools.log_filter import LogFilter
from common.logs.logger import app_logger
from common.client.hunyuan_client import HunyuanClient
from common.tools.file_utils import append_to_file
from common.error.detailed_value_error import raise_value_error, ErrorCode

class BaseAgent:
    def __init__(self, userAction, query, logs_path, log_tags, **kwargs):
        self._user_action = userAction
        self._query = query
        self._logs_path = logs_path
        self._log_tags = log_tags

        self._prompt = kwargs.get('prompt', '')
        self._bug_time = kwargs.get('bug_time', '')
        self._index_keys = kwargs.get('index_keys') or set()
        self._save_logs = kwargs.get('save_logs') or []
        self._delete_logs = kwargs.get('delete_logs') or []
        self._split_info = kwargs.get('split_info') or []
        self._dedup_targets = kwargs.get('dedup_targets') or []
        self._extract_fields = kwargs.get('extract_fields') or {}
        self._is_fuzzy_match_tag = kwargs.get('is_fuzzy_match_tag', False)
        self._is_analyze_daemon = kwargs.get('is_analyze_daemon', False)
        self._tag_ignore_patterns = kwargs.get('tag_ignore_patterns') or {}
        self._placeholder = kwargs.get('placeholder', '')
        self._ticket_id = kwargs.get('ticket_id', '')
        self._is_analyze_all_logs = kwargs.get('is_analyze_all_logs', False)

    def parse_log(self):
        app_logger.info('======= 开始 日志 分析 =========')

        log_filter = LogFilter(
            logs_path=self._logs_path,
            log_tags=self._log_tags,
            bug_time=self._bug_time,
            index_keys=self._index_keys,
            save_logs=self._save_logs, 
            delete_logs=self._delete_logs,
            split_info=self._split_info, 
            dedup_targets=self._dedup_targets, 
            extract_fields=self._extract_fields,
            is_fuzzy_match_tag=self._is_fuzzy_match_tag,
            is_analyze_daemon=self._is_analyze_daemon,
            tag_ignore_patterns=self._tag_ignore_patterns,
            placeholder=self._placeholder,
            is_analyze_all_logs=self._is_analyze_all_logs
        )
        app_logger.info(f"extract_fields = {self._extract_fields}, type = {type(self._extract_fields)}")
        try:
            filteredLogs, filteredLogs_daemon, rag, extracted_info = log_filter.get_filtered_log()
            # app_logger.info(f'filteredLogs = {"".join(filteredLogs)}')
        except Exception as e:
            raise_value_error(
                ErrorCode.IWIKI_PARSE_ERROR,
                message=f"iwiki配置解析失败\n错误信息：{e}\n请检查要求使用英文书写的字段，是否 是英文。若确保是英文，请联系lichenlin"
            )

        # 给 extracted_info 补默认空字符串
        safe_extracted_info = defaultdict(str, extracted_info)

        try:
            prompt = self._prompt.format(
            rag=rag if rag else "",
            user_action=self._user_action,
            log_content="".join(filteredLogs),
            daemon_log_content="".join(filteredLogs_daemon),
            query=self._query,
            **safe_extracted_info
            )
        except Exception as e:
            raise_value_error(
                ErrorCode.PROMPT_FORMAT_ERROR,
                message=f"Prompt格式错误\n错误信息：{e}\n请检查 是否填充了必要的占位符，如需要输入{{}}，请书写两个花括号{{{{}}}}，避免语法冲突"
            )
        
        if self._ticket_id:
            append_to_file(self._ticket_id, f'>>> 输入prompt\n{prompt}\n')


        # app_logger.info(f'prompt = {prompt}')

        yield from self._request_model(prompt)

    
    def _request_model(self, prompt):
        app_logger.info(prompt)
        ss_url = 'http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions'
        model = "DeepSeek-R1-Online"
        print(f"model = {model}")
        wsid = "11417"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = True
        enable_enhancement = False

        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream, enable_enhancement)
        # 使用 DeepSeek 专用方法
        yield from self._stream_results(hunyuan_client.request_deepseek(prompt))
    
    def _stream_results(self, results):
        for result in results:
            yield {"data": result['data'], "type": result['type']}

if __name__ == '__main__':
    log_tags = ['PageReporter_beaconReport', 'HttpProtocolInterceptor', 'HttpRequest',
                          'HTTPRequester', 'RuntimeCrash',
                          'ReceivingRewardViewModel', 'YybActCommonReceiveManager',
                          'YybLotteryView','YybLotteryViewModel']
    query = '活动参与失败'
    # bug_time = '2025-04-17 13:59:28'  
    # bug_time = None
    bug_time = '2025-04-18 23:49:25.000'
    prompt="""
这是Prompt
    """

    logs_path = '/Users/<USER>/Desktop/日志分析/activity - 用户问题不准确，aisee协助将分类输入到问题中/aisee/1. 领取-发货失败/1.我群星纪元首发活动，5w站力值领的2qb发送失败了，幸幸苦苦刚打完，发送失败怎么回事！要求人工重发！！！/TDOSLog_20250419_000553462_21752_58453'

    base_agent = BaseAgent(
        userAction="",
        query=query,
        logs_path=logs_path,
        log_tags=log_tags,
        bug_time=bug_time,
        prompt=prompt,
        is_fuzzy_match_tag=True
    )
    base_agent.parse_log()
