import json
from typing import <PERSON><PERSON>, Optional, Dict, Any

from common.logs.logger import app_logger
from common.error.detailed_value_error import raise_value_error, ErrorCode
from common.client.iwiki_client import IWikiClient
from common.tools.text_parse_utils import TextParseUtils
from common.tools.file_utils import append_to_file
from common.client.hunyuan_client import HunyuanClient
from version_tool.prompt.version_tool_prompt import INTENT_PARSER_PROMPT

class UserIntentParser:
    @staticmethod
    def parse_user_intent(query: str, ticket_id: Optional[str]) -> Tuple[str, Dict[str, Any]]:
        """
        解析用户意图

        :param query: 用户问题
        :param ticket_id: 工单ID，可选，用于日志写入
        :return: (user_query_scene, extracted_fields)
        """
        # 查询配置文件，获取已有预设场景
        iwiki_client = IWikiClient()

        # 用户意图场景及对应的 prompt iwiki 链接
        user_query_scene = ''
        extracted_fields = {}
        # user_intent_prompt = iwiki_client.get_doc_body("https://iwiki.woa.com/p/4015250765")
        # if not (user_intent_prompt and user_intent_prompt.lstrip().startswith('```')):
        #     # raise ValueError('未找到用户意图分析Prompt')
        #     raise_value_error(ErrorCode.USER_INTENT_PROMPT_NOT_FOUND, message="未找到用户意图分析Prompt")

        # user_intent_prompt = TextParseUtils.extract_code_block(user_intent_prompt)
        # user_intent_prompt = user_intent_prompt.format(query=query)
        # 使用本地prompt
        user_intent_prompt = INTENT_PARSER_PROMPT.format(query=query)

        items = UserIntentParser._request_model_for_user_intent(user_intent_prompt) #输出场景名称
        if items is None:
            app_logger.info("模型输出流式结果为空")
            raise_value_error(ErrorCode.MODEL_STREAM_RESULT_EMPTY, message="模型输出流式结果为空")

        for result in items:
            if result['type'] == 'all_answer':
                try:
                    data = json.loads(result['data'])
                    user_query_scene = data.get("issue_scene")
                    
                    extracted_fields = {
                        "qua_list": data.get("qua_list", "none"),
                        "app_version": data.get("app_version", "none"),
                        "app_version_hint": data.get("app_version_hint", "none"),
                        "start_time": data.get("start_time", "none"),
                        "end_time": data.get("end_time", "none"),
                        "time_scope_hint": data.get("time_scope_hint", "none"),
                        "node_name": data.get("node_name", "none")
                    }
                    
                    if ticket_id:
                        #记录日志信息
                        append_to_file(ticket_id, f'>>> 识别到的场景：\n{user_query_scene}\n')
                        append_to_file(ticket_id, f'>>> 提取的字段信息：\n{json.dumps(extracted_fields, ensure_ascii=False, indent=2)}\n')
                    return user_query_scene, extracted_fields
                    
                except json.JSONDecodeError as e:
                    app_logger.error(f"JSON解析错误: {str(e)}, 原始数据: {result['data']}")
                    raise_value_error(ErrorCode.USER_INTENT_SCENE_NOT_FOUND, message=f"解析模型返回结果失败: {str(e)}")

        # 如果没有返回，抛异常
        # raise ValueError('未识别到有效的用户意图场景')
        raise_value_error(ErrorCode.USER_INTENT_SCENE_NOT_FOUND, message="未识别到有效的用户意图场景")
    
    # 用户意图 model
    @staticmethod
    def _request_model_for_user_intent(prompt):
        app_logger.info(prompt)
        ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
        model = "70B-Dense-SFT-32K"  # DeepSeek-R1
        wsid = "10697"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = False
        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream)
        response = None
        for result in hunyuan_client.request(prompt):
            if result['type'] == 'all_answer':
                response = result
        return [response] if response else None

if __name__ == '__main__':
    test_cases = [
        "查询版本898的全部计划",
        "2024年8月1日到2025年6月9日这段时间有哪些版本",
        "898的覆盖率是多少",
        "TMAF_858_P_9521、TMAF_858_P_9522的覆盖率是多少",
        "我名下的需求有哪些",
        "899版本的所有实验和灰度分支情况",
        # hint识别测试
        "查询当前版本计划",
        "查询下一个版本计划",
        "查询上一个版本计划",
        "查询上个版本计划",
        "查询个版本计划"
    ]

    for query in test_cases:
        print("\n" + "="*50)
        print(f"测试查询: {query}")
        print("="*50)
        
        try:
            # 调用解析器
            scene, extracted_fields = UserIntentParser.parse_user_intent(query=query, ticket_id=None)
            
            print(f"\n识别场景: {scene}")
            print("\n提取字段:")
            print(json.dumps(extracted_fields, ensure_ascii=False, indent=2))

        except Exception as e:
            print(f"错误: {str(e)}")
            continue





