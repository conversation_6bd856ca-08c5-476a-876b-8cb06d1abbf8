#!/usr/bin/env python3
"""
AI异常总结Agent
负责生成异常数据的AI总结报告
"""

from typing import Generator, Dict, Any
from common.client.hunyuan_client import HunyuanClient
from version_tool.gray_data.agent.ai_anomaly_summary_prompt import AI_ANOMALY_SUMMARY_PROMPT


class AIAnomalySummaryAgent:
    """AI异常总结Agent"""

    def __init__(self, model: str = "DeepSeek-R1-Online"):
        """
        初始化AI异常总结Agent

        Args:
            model: 使用的AI模型名称
        """
        self.model = model
        self.ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
        self.token = "00ac8819-7488-4487-bfbd-17f4d760aed8"

    def generate_ai_summary(self, anomaly_summary: str) -> str:
        """
        生成AI异常总结报告

        Args:
            anomaly_summary: 原始异常数据汇总

        Returns:
            str: AI生成的异常总结报告
        """
        if not anomaly_summary or anomaly_summary.strip() == "未发现异常数据":
            return "未发现异常数据"
        
        print(f'原始异常数据汇总 = {anomaly_summary}')
        
        prompt = AI_ANOMALY_SUMMARY_PROMPT.format(anomaly_summary=anomaly_summary)
        print(f'AI异常总结prompt = {prompt}')

        results = self._request_model(prompt=prompt)
        ai_summary = ""

        for result in results:
            if result['type'] == 'all_answer':
                ai_summary = result['data']
                break

        return ai_summary

    def _request_model(self, prompt: str) -> Generator[Dict[str, Any], None, None]:
        """
        请求AI模型获取结果

        Args:
            prompt: 输入的提示词

        Yields:
            Dict[str, Any]: 模型返回的结果
        """
        # hunyuan_client = HunyuanClient(
        #     ss_url=self.ss_url,
        #     wsid=self.wsid,
        #     model=self.model,
        #     token=self.token,
        #     is_stream=self.is_stream
        # )

        # for result in hunyuan_client.request(prompt):
        #     yield {"data": result['data'], "type": result['type']}
        

        # deepseek 模型配置
        if "DeepSeek" in self.model:
            wsid = "11417"
            token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
            is_stream = True
            enable_enhancement = False

            hunyuan_client = HunyuanClient(self.ss_url, wsid, self.model, token, is_stream, enable_enhancement)
            # 使用 DeepSeek 专用方法
            yield from self._stream_results(hunyuan_client.request_deepseek(prompt))
        else:
            # Hunyuan 模型配置
            wsid = "10697"
            token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
            is_stream = False
            enable_enhancement = True

            hunyuan_client = HunyuanClient(self.ss_url, wsid, self.model, token, is_stream, enable_enhancement)
            # 使用 Hunyuan 原有方法
            yield from self._stream_results(hunyuan_client.request(prompt))
        
    
    def _stream_results(self, results):
        for result in results:
            yield {"data": result['data'], "type": result['type']}


def create_ai_anomaly_summary_agent(model: str = "Hunyuan-T1-32K") -> AIAnomalySummaryAgent:
    """
    创建AI异常总结Agent实例

    Args:
        model: 使用的AI模型名称

    Returns:
        AIAnomalySummaryAgent: AI异常总结Agent实例
    """
    return AIAnomalySummaryAgent(model=model)


if __name__ == "__main__":
    # 示例用法
    agent = create_ai_anomaly_summary_agent(model="DeepSeek-R1-Online")
    anomaly_summary = """
# [异常总结]
## Crash率异常
**老用户：**
- **实验组1** 第2天: `0.20%` (超过0.18%阈值(高出0.0186%)) `[QUA: 8.9.9_8994130_8549, 时间: 2025/06/07]`
- **实验组1** 第3天: `0.18%` (超过0.18%阈值(实际值:0.1817%)) `[QUA: 8.9.9_8994130_8549, 时间: 2025/06/08]`
- **实验组2** 第2天: `0.20%` (超过0.18%阈值(高出0.0152%)) `[QUA: 8.9.9_8994130_8551, 时间: 2025/06/07]`
- **实验组2** 第3天: `0.19%` (超过0.18%阈值(实际值:0.1875%)) `[QUA: 8.9.9_8994130_8551, 时间: 2025/06/08]`
"""

    anomaly_summary1 = """
# 异常数据

## Crash率异常
**老用户：**
- **实验组1** 第2天: `0.20%` (超过0.18%阈值(高出0.0186%)) `[QUA: 8.9.9_8994130_8549, 时间: 2025/06/07]`
- **实验组1** 第3天: `0.18%` (超过0.18%阈值(实际值:0.1817%)) `[QUA: 8.9.9_8994130_8549, 时间: 2025/06/08]`
- **实验组2** 第2天: `0.20%` (超过0.18%阈值(高出0.0152%)) `[QUA: 8.9.9_8994130_8551, 时间: 2025/06/07]`
- **实验组2** 第3天: `0.19%` (超过0.18%阈值(实际值:0.1875%)) `[QUA: 8.9.9_8994130_8551, 时间: 2025/06/08]`

## 启动速度异常
**新用户：**
- **实验组1** 常规外call冷启动: `3565.69 (176次)` (比对照组慢68.86ms) `[QUA: TMAF_899_P_8547, 时间: 2025/06/06~2025/06/08]`
- **实验组2** 常规外call冷启动: `3567.63 (163次)` (比对照组慢70.80ms) `[QUA: TMAF_899_P_8548, 时间: 2025/06/06~2025/06/08]`

**老用户：**
- **实验组1** 常规外call冷启动: `4825.99 (2566次)` (比对照组慢64.30ms) `[QUA: TMAF_899_P_8549, 时间: 2025/06/06~2025/06/08]`
"""

    anomaly_summary2 = """

# 异常数据

## 启动速度异常
**老用户：**
- **实验组1** 常规冷启动: `2576.77 (1930次)` (比对照组慢116.23ms) `[QUA: TMAF_899_P_9178, 时间: 2025/06/13~2025/06/16]`
- **实验组1** 常规外call冷启动: `5001.48 (514次)` (比对照组慢87.20ms) `[QUA: TMAF_899_P_9178, 时间: 2025/06/13~2025/06/16]`

## 下载安装CVR异常
**老用户：**
- **实验组1** 第1天: `58.06% (937数)` (比对照组低1.18%) `[QUA: TMAF_899_P_9178, 时间: 2025/06/13]`
- **实验组1** 第2天: `53.40% (2646数)` (比对照组低1.62%) `[QUA: TMAF_899_P_9178, 时间: 2025/06/14]`
- **实验组1** 第3天: `51.60% (2564数)` (比对照组低1.84%) `[QUA: TMAF_899_P_9178, 时间: 2025/06/15]`

## 广告转化率异常
**老用户：**
- **实验组1** 第1天: `145.74%` (比对照组低20.51%) `[QUA: TMAF_899_P_9178, 时间: 2025/06/13]`
"""
    ai_summary = agent.generate_ai_summary(anomaly_summary2)
    print(f'AI异常总结报告 = {ai_summary}')
