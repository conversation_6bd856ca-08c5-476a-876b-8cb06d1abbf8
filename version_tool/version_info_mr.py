import re
import os
from typing import List
from datetime import datetime, timedelta
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, Font
from aflowatom.tapd.tapd_base import TapdBase, TapdStory
from openpyxl import Workbook
import requests
from openpyxl.worksheet.worksheet import Worksheet

TAPD_APP_ID = 'yyb_tapd'
TAPD_TOKEN = '31F684F1-6405-28CB-03E2-E6B60B66780B'
WORKSPACE_ID = '20422314'
LAST_VERSION_BRANCH = os.getenv('LAST_VERSION_BRANCH', 'release-8.7.8')
HYPERLINK_FONT = Font(color='0000FF', underline='single')
DEFAULT_ALIGN = Alignment(horizontal='left', vertical='center', wrap_text=True)

# 常量定义
PROJECT_ID = '126979'
GIT_PROJECT_PREFIX = f'https://git.woa.com/api/v3/projects/{PROJECT_ID}'
PRIVATE_TOKEN = '_zhCOZW_1nf3_bHORMGD'
MR_PREFIX = 'https://git.woa.com/yyb-android/TencentMobileAssistant/merge_requests/'
TAPD_PREFIX = 'https://tapd.woa.com/20422314'

TAPD_PREFIX_MAP = {
    'bug': f'{TAPD_PREFIX}/bugtrace/bugs/view?bug_id=',
    'task': f'{TAPD_PREFIX}/prong/tasks/view/',
    'story': f'{TAPD_PREFIX}/prong/stories/view/',
}

COLUMNS = ["TAPD类型", "需求类别", "需求名字", "需求Owner", "MR地址", "MR作者", "TAPD地址", "MR描述", "实验地址",
           "开关", "测试说明", "修改代码"]

COLUMNS_LEN = {"TAPD类型": 10, "需求类别": 15, "需求名字": 50, "需求Owner": 15, "MR地址": 50, "MR作者": 20,
               "TAPD地址": 50, "MR描述": 30, "实验地址": 40,
               "开关": 30, "测试说明": 30, "修改代码": 120}


def get_url(tapd_type: str, mr_id: str) -> str:
    return TAPD_PREFIX_MAP[tapd_type] + mr_id


# 获取mr描述和标题
def get_mr_description(mr_id: int):
    url = f'{GIT_PROJECT_PREFIX}/merge_request/{mr_id}'
    params = {
        'private_token': PRIVATE_TOKEN
    }
    res = requests.get(url, params)
    if res.status_code != 200:
        raise RuntimeError('获取mr详情{}，请求结果不是200', mr_id, res.json())
    mr = res.json()
    return mr['title'], mr['description'], mr['author']['name']


# 根据最近2周的mr列表转换mr_iid到mr_id
def translate_mr_id_in_last_30day(mr_iids: List[str]) -> [str]:
    url = f'{GIT_PROJECT_PREFIX}/merge_requests'
    time_2week_ago = datetime.now() - timedelta(days=30)
    created_after_str = time_2week_ago.strftime('%Y-%m-%dT%H:%M:%S+0000')

    params = {
        'state': 'merged',
        'target_branch': 'master',
        'order_by': 'resolve_at',
        'per_page': 200,
        'created_after': created_after_str,
        'private_token': PRIVATE_TOKEN,
        'iids[]': mr_iids
    }
    res = requests.get(url, params)
    if res.status_code != 200:
        raise RuntimeError('获取mr列表信息失败，请求结果不是200', res.json())
    else:
        # 因所用字段不多，未做类型定义，返回类型请参考：https://git.woa.com/help/menu/api/
        # merge_requests.html#%E8%8E%B7%E5%8F%96%E5%90%88%E5%B9%B6%E8%AF%B7%E6%B1%82%E5%88%97%E8%A1%A8
        mr_list = res.json()
        out = [{'iid': mr['iid'], 'id': mr['id'], 'author': mr['author']['name']} if str(mr['iid']) in mr_iids else ''
               for mr in mr_list]
        return list(filter(lambda x: x != "", out))


# 从指定版本中获取和master差异的 mr_iid 列表
def get_mr_iid_list_from_version(version) -> [{str: str}]:
    url = f'{GIT_PROJECT_PREFIX}/repository/compare'
    params = {
        'id': PROJECT_ID,
        'from': version,
        'to': 'master',
        'private_token': PRIVATE_TOKEN
    }
    res = requests.get(url, params)
    if res.status_code != 200:
        raise RuntimeError('获取mr列表信息失败，请求结果不是200', res.json())
    else:
        # 返回类型请参考：https://git.woa.com/help/menu/api/
        # repositorys.html#%E8%8E%B7%E5%8F%96%E5%B7%AE%E5%BC%82%E5%86%85%E5%AE%B9
        mr_list = res.json()
        # 正则表达式匹配 "merge request !" 后跟一系列数字
        pattern = re.compile(r'merge request !(\d+)')
        title_list = list(map(lambda mr: mr['title'], mr_list['commits']))
        # 使用map应用正则表达式匹配，并使用列表推导提取数字
        print('origin commits num is : {}'.format(len(title_list)))
        print('following items do not has mr num:')
        for item in title_list :
            if 'merge request !' not in item:
                print("***************{}".format(item))
        extracted_numbers = [match.group(1) for match in map(pattern.search, title_list) if match]
        print('commits has mr num is : {}'.format(len(extracted_numbers)))
        return extracted_numbers


def get_tapd_info_by_id(tapd_id: str) -> {}:
    story = TapdStory.create(WORKSPACE_ID, id=tapd_id)
    story.load()

    # 如果是产品运营需求, 且是Story的，需要更新创建人为产品运营负责人
    if story['父需求'] != '0' and story['需求颗粒度'] == 'User Story' and story.get_display_value(
            '需求类型') == '产品与运营需求':
        story = TapdStory.create(WORKSPACE_ID, id=story['父需求'])
        story.load()

    return {
        '创建人': story.get_display_value('创建人'),
        '需求类别': story.get_display_value('需求类型'),
        '实验链接': story.get_display_value('实验链接'),
        '开关控制方式': story.get_display_value('开关控制方式')
    }


# 根据查询到的tapd信息和mr信息填充最终的需求列表
def fill_mr_tapds(version_mr, tapd_query, mr_info):
    for t in tapd_query:
        mr_url = MR_PREFIX + str(mr_info['iid'])
        if t['name'] in version_mr:
            version_mr[t['name']]['mr_list'].append(
                {'url': mr_url, 'title': mr_info['title'], 'author': mr_info.get('author')}, )
        else:
            version_mr[t['name']] = {
                'mr_list': [{'url': mr_url,
                             'title': mr_info['title'],
                             'author': mr_info.get('author'),
                             'dirs': mr_info.get('dirs')}],
                'tapd_id': get_url(t['tapd_type'], str(t['tapd_id'])),
                'tapd_type': t['tapd_type'],
                '实验': mr_info.get('实验'),
                '开关': mr_info.get('开关'),
                '测试说明': mr_info.get('测试说明'),
            }
            if t['tapd_type'] == 'story':
                version_mr[t['name']].update(get_tapd_info_by_id(t['tapd_id']))
            else:
                version_mr[t['name']].update({'创建人': "", '需求类别': "", })


def get_change_files_in_mr(mr_info) -> [{str: str}]:
    # GET / api / v3 / projects /: id / merge_request /:merge_request_id / changes
    mr_id = mr_info['id']
    url = f'{GIT_PROJECT_PREFIX}/merge_request/{mr_id}/changes'
    params = {
        'private_token': PRIVATE_TOKEN  # @WARN: 从环境变量中读取，确保改变量在流水线执行环境中有设置 GIT_PRIVATE_TOKEN
    }
    res = requests.get(url, params)
    if res.status_code != 200:
        raise RuntimeError('获取文件息失败(根据mr)，请求结果不是200', res.json())
    else:
        file_info = res.json()
        directories = []
        pattern = re.compile(r"diff --git a/(.+?) b/\1")
        for file in file_info['files']:
            # 正则表达式匹配 Unix 风格的目录路径
            # 查找所有匹配的目录路径
            directories = directories + re.findall(pattern, file['diff'])
        directories = list(set(directories))
        contains_plugin = any(directory.startswith("Plugin/") for directory in directories)

        if contains_plugin:
            mr_iid = mr_info['iid']
            print(f'*********** Mr[{mr_iid}] is Plugin,will not to collect')
        else:
            mr_info['dirs'] = directories


# 获取相关的tapd信息
def get_related_tapd_info_list_by_mr_iid(mr_info, version_mr) -> [{str: str}]:
    url = f'{GIT_PROJECT_PREFIX}/tapd_workitems'
    if mr_info.get('dirs') is None:
        print(f'*********** Mr[{mr_info["title"]}] dir is None ,will not to collect')
        return
    params = {
        'type': 'mr',
        'iid': mr_info['iid'],
        'private_token': PRIVATE_TOKEN  # @WARN: 从环境变量中读取，确保改变量在流水线执行环境中有设置 GIT_PRIVATE_TOKEN
    }
    res = requests.get(url, params)
    if res.status_code != 200:
        raise RuntimeError('获取tapd信息失败(根据mr)，请求结果不是200', res.json())
    else:
        tapd_query = res.json()
        fill_mr_tapds(version_mr, tapd_query, mr_info)


# 合并指定区域单元格并设置对齐方式
def do_merge(ws: Worksheet, start_column, start_row, end_row):
    ws.merge_cells(start_row=start_row, start_column=start_column, end_row=end_row,
                   end_column=start_column)
    ws[get_column_letter(start_column) + str(start_row)].alignment = DEFAULT_ALIGN


# 合并临近的相同的单元格
def merge_cell(ws: Worksheet, column, related_columns=[]):
    prev_value = None
    merge_start_row = 1

    for cell in column:
        if cell.value != prev_value:
            if merge_start_row != cell.row:
                do_merge(ws, cell.column, merge_start_row, cell.row - 1)
                for same_column in related_columns:
                    do_merge(ws, same_column, merge_start_row, cell.row - 1)
            merge_start_row = cell.row
        prev_value = cell.value
    if merge_start_row != ws.max_row:
        do_merge(ws, column[0].column, merge_start_row, ws.max_row)


def to_columns_index(cols: List[str]) -> List[int]:
    # tapd_type -> tapd_type_index
    return [index + 1 for index, column in enumerate(COLUMNS) if column in cols]


def append_one_line(ws, tapd_type, name, tapd_info, mr_info):
    tab = tapd_info.get("实验") or ''
    if tapd_info.get("实验链接"):
        tab += '（TAPD实验：' + tapd_info.get("实验链接") + '）'
    switch = tapd_info.get("开关") or ''
    if tapd_info.get("开关控制方式"):
        switch += '（TAPD开关：' + tapd_info.get("开关控制方式") + '）'

    ws.append(
        [tapd_type, tapd_info.get('需求类别'), name, tapd_info.get('创建人'), mr_info.get('url'), mr_info.get('author'),
         tapd_info.get("tapd_id"),
         mr_info.get('title'),
         tab,
         switch,
         tapd_info.get("测试说明"),
         ';\n'.join(mr_info.get("dirs") if mr_info.get("dirs") else []),
         ])

    # 生成excel文件


# 定义一个函数，用于合并工作表中选定的列
def __merge_selected_columns(ws: Worksheet, related_columns_map: dict):
    for column in ws.columns:
        # 遍历相关列映射字典，查找与当前列名称匹配的选定列
        for selected_column, related_columns in related_columns_map.items():
            # 如果找到匹配的列名
            if selected_column == column[0].value:
                # 调用合并单元格的函数，传入当前列和相关列的索引
                merge_cell(ws, column, related_columns=to_columns_index(related_columns))


# 遍历JSON数据并写入Excel
def __init_excel(ws: Worksheet, data: dict):
    # 写入表头
    ws.append(COLUMNS)

    for tapd_type, items in data.items():
        sorted_items = sorted(items, key=lambda x: list(x.values())[0]['需求类别'])
        for item in sorted_items:
            name = list(item.keys())[0]
            tapd_info = item[name]
            mr_list = tapd_info.get("mr_list")

            # 如果mr_list包含多个元素，分别写入不同的行
            for mr in mr_list:
                append_one_line(ws, tapd_type, name, tapd_info, mr)


def __change_styles(ws: Worksheet):
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=COLUMNS.index("MR地址"),
                            max_col=len(COLUMNS)):
        for cell in row:
            cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
            if 'https' in str(cell.value):
                cell.hyperlink = cell.value  # 设置单元格的超链接
                cell.font = HYPERLINK_FONT  # 设置超链接的字体样式


def __change_columns_width(ws: Worksheet):
    for column in ws.columns:
        column_letter = get_column_letter(column[0].column)
        ws.column_dimensions[column_letter].width = COLUMNS_LEN[column[0].value]


def generate_excel(data: dict) -> None:
    # 创建一个新的Excel工作簿
    wb = Workbook()
    ws = wb.active

    __init_excel(ws, data)
    __change_styles(ws)

    # 合并指定列的内容
    __merge_selected_columns(ws, {
        'TAPD类型': [],
        '需求类别': [],
        'MR地址': ['MR描述', 'MR作者', '修改代码'],
        'TAPD地址': ['需求名字', "需求Owner", '实验地址', '开关', '测试说明']
    })
    __change_columns_width(ws)

    # 保存Excel文件
    wb.save('output.xlsx')


def fill_mr_desc(mr: {}, content: str) -> None:
    report_url_pattern = r"【实验报告地址】.*\n(.+)"
    report_url_match = re.search(report_url_pattern, content)
    if report_url_match:
        addr = report_url_match.group(1)
        if 'https://tab.woa.com/xxx' not in addr:
            mr['实验'] = report_url_match.group(1).replace('*', '')
    else:
        print("if no report mr {} is {}".format(mr['title'], content))

    # 使用正则表达式提取开关说明下一行的内容
    switch_pattern = r"【开关说明】.*\n(.+)"
    switch_match = re.search(switch_pattern, content)
    if switch_match:
        switch = switch_match.group(1)
        if '开关名称： xxxx' not in switch:
            mr['开关'] = switch_match.group(1)
    else:
        print("if no switch mr {} is {}".format(mr['title'], content))

    # 使用正则表达式提取测试说明下一行的内容
    test_pattern = r"【测试说明】.*\n(.+)"
    test_match = re.search(test_pattern, content)
    if test_match:
        mr['测试说明'] = test_match.group(1)


# 按照tapd类型排序
def sort_by_tapd_type(version_mr: dict):
    new_data = {}
    for key, value in version_mr.items():
        tapd_type = value.pop("tapd_type")
        if tapd_type not in new_data:
            new_data[tapd_type] = []
        new_data[tapd_type].append({key: value})
    return new_data


# 按照tapd类型排序
def sort_by_need_type(version_mr):
    new_data = {}
    for key, value in version_mr.items():
        tapd_type = value.pop("需求类别")
        if tapd_type not in new_data:
            new_data[tapd_type] = []
        new_data[tapd_type].append({key: value})
    return new_data


def make_up_change_info(version: str) -> None:
    # 获取过去最近master和指定分支的差别
    mr_iids = get_mr_iid_list_from_version(version)
    print("get mr info : {}".format(mr_iids))
    mr_ids = translate_mr_id_in_last_30day(mr_iids)
    print("get mr id count {}".format(len(mr_ids)))
    version_mr = {}
    for mr_id in mr_ids:
        mr_id['title'], desc, mr_id['author'] = get_mr_description(mr_id['id'])
        fill_mr_desc(mr_id, desc)
        get_change_files_in_mr(mr_id)
        get_related_tapd_info_list_by_mr_iid(mr_id, version_mr)

    generate_excel(sort_by_tapd_type(version_mr))
    print("generate_excel done")


TapdBase.set_default_client(TAPD_APP_ID, TAPD_TOKEN)
make_up_change_info(LAST_VERSION_BRANCH)
